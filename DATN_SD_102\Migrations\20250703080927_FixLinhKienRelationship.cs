﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace F4_API.Migrations
{
    /// <inheritdoc />
    public partial class FixLinhKienRelationship : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LinhKienCTs_LinhKiens_LinhKienLkId",
                table: "LinhKienCTs");

            migrationBuilder.DropIndex(
                name: "IX_LinhKienCTs_LinhKienLkId",
                table: "LinhKienCTs");

            migrationBuilder.DropColumn(
                name: "<PERSON>h<PERSON>ienLkId",
                table: "LinhKienCTs");

            migrationBuilder.UpdateData(
                table: "NhanViens",
                keyColumn: "NhanVienId",
                keyValue: new Guid("*************-8888-8888-************"),
                columns: new[] { "NgayCapNhatCuoiCung", "<PERSON>ay<PERSON>ao" },
                values: new object[] { new DateTime(2025, 7, 3, 15, 9, 23, 771, DateTimeKind.Local).AddTicks(2789), new DateTime(2025, 7, 3, 15, 9, 23, 771, DateTimeKind.Local).AddTicks(2788) });

            migrationBuilder.UpdateData(
                table: "TaiKhoans",
                keyColumn: "TaiKhoanId",
                keyValue: new Guid("*************-9999-9999-************"),
                column: "NgayTaoTaiKhoan",
                value: new DateTime(2025, 7, 3, 15, 9, 23, 771, DateTimeKind.Local).AddTicks(2701));

            migrationBuilder.CreateIndex(
                name: "IX_LinhKienCTs_LkId",
                table: "LinhKienCTs",
                column: "LkId");

            migrationBuilder.AddForeignKey(
                name: "FK_LinhKienCTs_LinhKiens_LkId",
                table: "LinhKienCTs",
                column: "LkId",
                principalTable: "LinhKiens",
                principalColumn: "LkId",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LinhKienCTs_LinhKiens_LkId",
                table: "LinhKienCTs");

            migrationBuilder.DropIndex(
                name: "IX_LinhKienCTs_LkId",
                table: "LinhKienCTs");

            migrationBuilder.AddColumn<Guid>(
                name: "LinhKienLkId",
                table: "LinhKienCTs",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "NhanViens",
                keyColumn: "NhanVienId",
                keyValue: new Guid("*************-8888-8888-************"),
                columns: new[] { "NgayCapNhatCuoiCung", "NgayTao" },
                values: new object[] { new DateTime(2025, 7, 3, 9, 2, 38, 429, DateTimeKind.Local).AddTicks(2698), new DateTime(2025, 7, 3, 9, 2, 38, 429, DateTimeKind.Local).AddTicks(2698) });

            migrationBuilder.UpdateData(
                table: "TaiKhoans",
                keyColumn: "TaiKhoanId",
                keyValue: new Guid("*************-9999-9999-************"),
                column: "NgayTaoTaiKhoan",
                value: new DateTime(2025, 7, 3, 9, 2, 38, 429, DateTimeKind.Local).AddTicks(2664));

            migrationBuilder.CreateIndex(
                name: "IX_LinhKienCTs_LinhKienLkId",
                table: "LinhKienCTs",
                column: "LinhKienLkId");

            migrationBuilder.AddForeignKey(
                name: "FK_LinhKienCTs_LinhKiens_LinhKienLkId",
                table: "LinhKienCTs",
                column: "LinhKienLkId",
                principalTable: "LinhKiens",
                principalColumn: "LkId");
        }
    }
}
